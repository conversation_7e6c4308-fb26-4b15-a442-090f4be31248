﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;
using Npgsql.EntityFrameworkCore.PostgreSQL.Metadata;

#nullable disable

#pragma warning disable CA1814 // Prefer jagged arrays over multidimensional

namespace NafaPlace.Coupon.Infrastructure.Migrations
{
    /// <inheritdoc />
    public partial class InitialCreate : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.CreateTable(
                name: "Coupons",
                columns: table => new
                {
                    Id = table.Column<int>(type: "integer", nullable: false)
                        .Annotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityByDefaultColumn),
                    Code = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: false),
                    Name = table.Column<string>(type: "character varying(200)", maxLength: 200, nullable: false),
                    Description = table.Column<string>(type: "character varying(500)", maxLength: 500, nullable: true),
                    Type = table.Column<int>(type: "integer", nullable: false),
                    Value = table.Column<decimal>(type: "numeric(18,2)", nullable: false),
                    MinimumOrderAmount = table.Column<decimal>(type: "numeric(18,2)", nullable: true),
                    MaximumDiscountAmount = table.Column<decimal>(type: "numeric(18,2)", nullable: true),
                    StartDate = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    EndDate = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    UsageLimit = table.Column<int>(type: "integer", nullable: true),
                    UsageLimitPerUser = table.Column<int>(type: "integer", nullable: true),
                    UsageCount = table.Column<int>(type: "integer", nullable: false, defaultValue: 0),
                    IsActive = table.Column<bool>(type: "boolean", nullable: false, defaultValue: true),
                    Currency = table.Column<string>(type: "character varying(3)", maxLength: 3, nullable: false, defaultValue: "GNF"),
                    ApplicableToAllProducts = table.Column<bool>(type: "boolean", nullable: false, defaultValue: true),
                    ApplicableProductIds = table.Column<string>(type: "text", nullable: true),
                    ApplicableCategoryIds = table.Column<string>(type: "text", nullable: true),
                    ApplicableSellerIds = table.Column<string>(type: "text", nullable: true),
                    ExcludedProductIds = table.Column<string>(type: "text", nullable: true),
                    ExcludedCategoryIds = table.Column<string>(type: "text", nullable: true),
                    ExcludedSellerIds = table.Column<string>(type: "text", nullable: true),
                    MinOrderAmount = table.Column<decimal>(type: "numeric", nullable: true),
                    CreatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    UpdatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    CreatedBy = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: true),
                    UpdatedBy = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_Coupons", x => x.Id);
                });

            migrationBuilder.CreateTable(
                name: "CouponUsages",
                columns: table => new
                {
                    Id = table.Column<int>(type: "integer", nullable: false)
                        .Annotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityByDefaultColumn),
                    CouponId = table.Column<int>(type: "integer", nullable: false),
                    UserId = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: false),
                    OrderId = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: true),
                    DiscountAmount = table.Column<decimal>(type: "numeric(18,2)", nullable: false),
                    Currency = table.Column<string>(type: "character varying(3)", maxLength: 3, nullable: false, defaultValue: "GNF"),
                    UsedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: false, defaultValueSql: "CURRENT_TIMESTAMP"),
                    CreatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    UpdatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    CreatedBy = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: true),
                    UpdatedBy = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_CouponUsages", x => x.Id);
                    table.ForeignKey(
                        name: "FK_CouponUsages_Coupons_CouponId",
                        column: x => x.CouponId,
                        principalTable: "Coupons",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.InsertData(
                table: "Coupons",
                columns: new[] { "Id", "ApplicableCategoryIds", "ApplicableProductIds", "ApplicableSellerIds", "ApplicableToAllProducts", "Code", "CreatedAt", "CreatedBy", "Currency", "Description", "EndDate", "ExcludedCategoryIds", "ExcludedProductIds", "ExcludedSellerIds", "IsActive", "MaximumDiscountAmount", "MinOrderAmount", "MinimumOrderAmount", "Name", "StartDate", "Type", "UpdatedAt", "UpdatedBy", "UsageLimit", "UsageLimitPerUser", "Value" },
                values: new object[,]
                {
                    { 1, null, null, null, true, "WELCOME10", new DateTime(2025, 7, 17, 23, 15, 3, 516, DateTimeKind.Utc).AddTicks(5216), "System", "GNF", "10% de réduction pour les nouveaux clients", new DateTime(2026, 7, 17, 23, 15, 3, 516, DateTimeKind.Utc).AddTicks(4769), null, null, null, true, 100000m, null, 50000m, "Bienvenue - 10% de réduction", new DateTime(2025, 6, 17, 23, 15, 3, 516, DateTimeKind.Utc).AddTicks(4661), 2, new DateTime(2025, 7, 17, 23, 15, 3, 516, DateTimeKind.Utc).AddTicks(5278), null, 1000, 1, 10m },
                    { 2, null, null, null, true, "FREESHIP", new DateTime(2025, 7, 17, 23, 15, 3, 516, DateTimeKind.Utc).AddTicks(5342), "System", "GNF", "Livraison gratuite pour toute commande", new DateTime(2025, 8, 16, 23, 15, 3, 516, DateTimeKind.Utc).AddTicks(5340), null, null, null, true, null, null, 100000m, "Livraison gratuite", new DateTime(2025, 7, 10, 23, 15, 3, 516, DateTimeKind.Utc).AddTicks(5340), 3, new DateTime(2025, 7, 17, 23, 15, 3, 516, DateTimeKind.Utc).AddTicks(5342), null, 500, null, 25000m },
                    { 3, null, null, null, true, "SAVE50K", new DateTime(2025, 7, 17, 23, 15, 3, 516, DateTimeKind.Utc).AddTicks(5346), "System", "GNF", "50,000 GNF de réduction sur votre commande", new DateTime(2025, 7, 31, 23, 15, 3, 516, DateTimeKind.Utc).AddTicks(5345), null, null, null, true, null, null, 200000m, "Économisez 50,000 GNF", new DateTime(2025, 7, 17, 23, 15, 3, 516, DateTimeKind.Utc).AddTicks(5345), 1, new DateTime(2025, 7, 17, 23, 15, 3, 516, DateTimeKind.Utc).AddTicks(5347), null, 100, 1, 50000m }
                });

            migrationBuilder.CreateIndex(
                name: "IX_Coupons_Code",
                table: "Coupons",
                column: "Code",
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_Coupons_EndDate",
                table: "Coupons",
                column: "EndDate");

            migrationBuilder.CreateIndex(
                name: "IX_Coupons_IsActive",
                table: "Coupons",
                column: "IsActive");

            migrationBuilder.CreateIndex(
                name: "IX_Coupons_IsActive_StartDate_EndDate",
                table: "Coupons",
                columns: new[] { "IsActive", "StartDate", "EndDate" });

            migrationBuilder.CreateIndex(
                name: "IX_Coupons_StartDate",
                table: "Coupons",
                column: "StartDate");

            migrationBuilder.CreateIndex(
                name: "IX_CouponUsages_CouponId",
                table: "CouponUsages",
                column: "CouponId");

            migrationBuilder.CreateIndex(
                name: "IX_CouponUsages_CouponId_UserId",
                table: "CouponUsages",
                columns: new[] { "CouponId", "UserId" });

            migrationBuilder.CreateIndex(
                name: "IX_CouponUsages_OrderId",
                table: "CouponUsages",
                column: "OrderId");

            migrationBuilder.CreateIndex(
                name: "IX_CouponUsages_UsedAt",
                table: "CouponUsages",
                column: "UsedAt");

            migrationBuilder.CreateIndex(
                name: "IX_CouponUsages_UserId",
                table: "CouponUsages",
                column: "UserId");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "CouponUsages");

            migrationBuilder.DropTable(
                name: "Coupons");
        }
    }
}
