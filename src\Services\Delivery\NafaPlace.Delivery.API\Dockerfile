FROM mcr.microsoft.com/dotnet/aspnet:9.0 AS base
USER app
EXPOSE 8080
EXPOSE 8081

FROM mcr.microsoft.com/dotnet/sdk:9.0 AS build
ARG BUILD_CONFIGURATION=Release
WORKDIR /src
COPY ["src/Services/Delivery/NafaPlace.Delivery.API/NafaPlace.Delivery.API.csproj", "src/Services/Delivery/NafaPlace.Delivery.API/"]
COPY ["src/Services/Delivery/NafaPlace.Delivery.Application/NafaPlace.Delivery.Application.csproj", "src/Services/Delivery/NafaPlace.Delivery.Application/"]
COPY ["src/Services/Delivery/NafaPlace.Delivery.Domain/NafaPlace.Delivery.Domain.csproj", "src/Services/Delivery/NafaPlace.Delivery.Domain/"]
COPY ["src/BuildingBlocks/Common/NafaPlace.Common/NafaPlace.Common.csproj", "src/BuildingBlocks/Common/NafaPlace.Common/"]
COPY ["src/Services/Delivery/NafaPlace.Delivery.Infrastructure/NafaPlace.Delivery.Infrastructure.csproj", "src/Services/Delivery/NafaPlace.Delivery.Infrastructure/"]
RUN dotnet restore "./src/Services/Delivery/NafaPlace.Delivery.API/NafaPlace.Delivery.API.csproj"
COPY . .
WORKDIR "/src/src/Services/Delivery/NafaPlace.Delivery.API"
RUN dotnet build "NafaPlace.Delivery.API.csproj" -c $BUILD_CONFIGURATION -o /app/build

FROM build AS publish
ARG BUILD_CONFIGURATION=Release
RUN dotnet publish "NafaPlace.Delivery.API.csproj" -c $BUILD_CONFIGURATION -o /app/publish /p:UseAppHost=false

FROM base AS final
WORKDIR /app
COPY --from=publish /app/publish .
ENTRYPOINT ["dotnet", "NafaPlace.Delivery.API.dll"]
