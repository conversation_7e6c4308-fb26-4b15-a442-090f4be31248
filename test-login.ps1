# Test simple de login
$loginData = @{
    Username = "<EMAIL>"
    Password = "Admin123!"
}

Write-Host "Test de login..." -ForegroundColor Yellow

try {
    $response = Invoke-RestMethod -Uri "http://localhost:5155/api/auth/login" -Method POST -Body ($loginData | ConvertTo-Json) -ContentType "application/json"
    Write-Host "Login reussi!" -ForegroundColor Green
    Write-Host "Token: $($response.accessToken.Substring(0, 50))..." -ForegroundColor Cyan

    # Test avec l'API Gateway
    Write-Host "`nTest via API Gateway..." -ForegroundColor Yellow
    $gatewayResponse = Invoke-RestMethod -Uri "http://localhost:5000/api/identity/auth/login" -Method POST -Body ($loginData | ConvertTo-Json) -ContentType "application/json"
    Write-Host "Login via Gateway reussi!" -ForegroundColor Green
    Write-Host "Token: $($gatewayResponse.accessToken.Substring(0, 50))..." -ForegroundColor Cyan

} catch {
    Write-Host "Erreur: $($_.Exception.Message)" -ForegroundColor Red
}
