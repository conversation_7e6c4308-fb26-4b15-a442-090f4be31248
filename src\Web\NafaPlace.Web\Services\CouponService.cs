using System.Net.Http.Json;
using System.Text.Json;
using NafaPlace.Web.Models;
using Microsoft.AspNetCore.Components.Authorization;
using Blazored.LocalStorage;
using System.Net.Http.Headers;

namespace NafaPlace.Web.Services;

public class CouponService : ICouponService
{
    private readonly HttpClient _httpClient;
    private readonly ILogger<CouponService> _logger;
    private readonly string _baseUrl;
    private readonly AuthenticationStateProvider _authStateProvider;
    private readonly ILocalStorageService _localStorage;

    public CouponService(HttpClient httpClient, IConfiguration configuration, ILogger<CouponService> logger,
        AuthenticationStateProvider authStateProvider, ILocalStorageService localStorage)
    {
        _httpClient = httpClient;
        _logger = logger;
        _baseUrl = configuration["ApiEndpoints:CouponsApi"] ?? "http://localhost:5009/api/coupon";
        _authStateProvider = authStateProvider;
        _localStorage = localStorage;
    }

    private async Task SetAuthorizationHeaderAsync()
    {
        try
        {
            var authState = await _authStateProvider.GetAuthenticationStateAsync();
            if (authState.User.Identity?.IsAuthenticated == true)
            {
                var token = await _localStorage.GetItemAsync<string>("authToken");
                if (!string.IsNullOrEmpty(token))
                {
                    // Supprimer les guillemets éventuels autour du token
                    token = token.Trim('"');
                    _httpClient.DefaultRequestHeaders.Authorization =
                        new AuthenticationHeaderValue("Bearer", token);
                }
            }
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "Failed to set authorization header");
        }
    }

    public async Task<CouponValidationResult?> ValidateCouponAsync(string couponCode, CartSummary cart)
    {
        try
        {
            await SetAuthorizationHeaderAsync();
            var request = new
            {
                CouponCode = couponCode,
                Cart = new
                {
                    UserId = "", // Sera rempli par l'API si nécessaire
                    Items = cart.Items.Select(item => new
                    {
                        ProductId = item.ProductId,
                        ProductName = "", // Pas nécessaire pour la validation
                        UnitPrice = item.UnitPrice,
                        Quantity = item.Quantity,
                        CategoryId = item.CategoryId,
                        SellerId = item.SellerId
                    }).ToList(),
                    SubTotal = cart.TotalAmount,
                    Currency = cart.Currency
                }
            };

            var response = await _httpClient.PostAsJsonAsync($"{_baseUrl}/validate", request);
            
            if (response.IsSuccessStatusCode)
            {
                var content = await response.Content.ReadAsStringAsync();
                return JsonSerializer.Deserialize<CouponValidationResult>(content, new JsonSerializerOptions
                {
                    PropertyNameCaseInsensitive = true
                });
            }
            
            _logger.LogWarning("Failed to validate coupon {CouponCode}. Status: {StatusCode}", couponCode, response.StatusCode);
            return null;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error validating coupon {CouponCode}", couponCode);
            return null;
        }
    }

    public async Task<CouponApplicationResult?> ApplyCouponAsync(string couponCode, CartSummary cart)
    {
        try
        {
            await SetAuthorizationHeaderAsync();
            var request = new
            {
                CouponCode = couponCode,
                Cart = new
                {
                    UserId = "", // Sera rempli par l'API si nécessaire
                    Items = cart.Items.Select(item => new
                    {
                        ProductId = item.ProductId,
                        ProductName = "", // Pas nécessaire pour la validation
                        UnitPrice = item.UnitPrice,
                        Quantity = item.Quantity,
                        CategoryId = item.CategoryId,
                        SellerId = item.SellerId
                    }).ToList(),
                    SubTotal = cart.TotalAmount,
                    Currency = cart.Currency
                }
            };

            var response = await _httpClient.PostAsJsonAsync($"{_baseUrl}/apply", request);
            
            if (response.IsSuccessStatusCode)
            {
                var content = await response.Content.ReadAsStringAsync();
                return JsonSerializer.Deserialize<CouponApplicationResult>(content, new JsonSerializerOptions
                {
                    PropertyNameCaseInsensitive = true
                });
            }
            
            _logger.LogWarning("Failed to apply coupon {CouponCode}. Status: {StatusCode}", couponCode, response.StatusCode);
            return null;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error applying coupon {CouponCode}", couponCode);
            return null;
        }
    }

    public async Task<List<CouponDto>> GetAvailableCouponsAsync(string userId, CartSummary cart)
    {
        try
        {
            var request = new
            {
                UserId = userId,
                Cart = new
                {
                    TotalAmount = cart.TotalAmount,
                    Items = cart.Items.Select(item => new
                    {
                        ProductId = item.ProductId,
                        Quantity = item.Quantity,
                        UnitPrice = item.UnitPrice,
                        CategoryId = item.CategoryId,
                        SellerId = item.SellerId
                    }).ToList()
                }
            };

            var response = await _httpClient.PostAsJsonAsync($"{_baseUrl}/available", request);
            
            if (response.IsSuccessStatusCode)
            {
                var content = await response.Content.ReadAsStringAsync();
                return JsonSerializer.Deserialize<List<CouponDto>>(content, new JsonSerializerOptions
                {
                    PropertyNameCaseInsensitive = true
                }) ?? new List<CouponDto>();
            }
            
            return new List<CouponDto>();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting available coupons for user {UserId}", userId);
            return new List<CouponDto>();
        }
    }

    public async Task<bool> RecordCouponUsageAsync(int couponId, string userId, string orderId, decimal discountAmount)
    {
        try
        {
            var request = new
            {
                CouponId = couponId,
                UserId = userId,
                OrderId = orderId,
                DiscountAmount = discountAmount
            };

            var response = await _httpClient.PostAsJsonAsync($"{_baseUrl}/record-usage", request);
            return response.IsSuccessStatusCode;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error recording coupon usage for coupon {CouponId}", couponId);
            return false;
        }
    }
}

// DTOs pour l'application web
public class CouponDto
{
    public int Id { get; set; }
    public string Code { get; set; } = string.Empty;
    public string Name { get; set; } = string.Empty;
    public string? Description { get; set; }
    public CouponType Type { get; set; }
    public decimal Value { get; set; }
    public decimal? MinimumOrderAmount { get; set; }
    public decimal? MaximumDiscountAmount { get; set; }
    public DateTime StartDate { get; set; }
    public DateTime EndDate { get; set; }
    public int? UsageLimit { get; set; }
    public int? UsageLimitPerUser { get; set; }
    public int UsageCount { get; set; }
    public bool IsActive { get; set; }
    public string Currency { get; set; } = "GNF";
}

public class CouponValidationResult
{
    public bool IsValid { get; set; }
    public string? ErrorMessage { get; set; }
    public decimal DiscountAmount { get; set; }
    public CouponDto? Coupon { get; set; }
}

public class CouponApplicationResult
{
    public bool IsApplied { get; set; }
    public string? ErrorMessage { get; set; }
    public decimal DiscountAmount { get; set; }
    public decimal NewTotalAmount { get; set; }
    public CouponDto? AppliedCoupon { get; set; }
}

public enum CouponType
{
    FixedAmount = 1,
    Percentage = 2,
    FreeShipping = 3,
    BuyXGetY = 4
}
