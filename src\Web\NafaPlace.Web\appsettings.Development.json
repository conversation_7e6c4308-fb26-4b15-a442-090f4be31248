{"ApiEndpoints": {"IdentityApi": "http://localhost:5001/api/identity", "CatalogApi": "http://localhost:5002/api/catalog", "CartApi": "http://localhost:5003/api/cart", "OrderApi": "http://localhost:5004/api/orders", "PaymentApi": "http://localhost:5005/api/payments", "ReviewApi": "http://localhost:5006/api/reviews", "NotificationsApi": "http://localhost:5007/api/notifications", "WishlistApi": "http://localhost:5008/api/wishlist", "CouponsApi": "http://localhost:5009/api/coupon"}, "Logging": {"LogLevel": {"Default": "Information", "Microsoft.AspNetCore": "Warning", "Microsoft.Hosting.Lifetime": "Information"}}}