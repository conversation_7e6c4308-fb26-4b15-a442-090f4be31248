# Test direct de l'API Coupon
Write-Host "=== Test direct de l'API Coupon ===" -ForegroundColor Green

# 1. Login pour obtenir un token
$loginData = @{
    Username = "<EMAIL>"
    Password = "Admin123!"
}

try {
    $loginResponse = Invoke-RestMethod -Uri "http://localhost:5155/api/auth/login" -Method POST -Body ($loginData | ConvertTo-Json) -ContentType "application/json"
    $token = $loginResponse.accessToken
    Write-Host "Token obtenu: $($token.Substring(0, 50))..." -ForegroundColor Green
} catch {
    Write-Host "Erreur de login: $($_.Exception.Message)" -ForegroundColor Red
    exit 1
}

$headers = @{
    "Authorization" = "Bearer $token"
    "Content-Type" = "application/json"
}

# 2. Test direct de l'API Coupon
Write-Host "`nTest de l'API Coupon sur le port 5009..." -ForegroundColor Yellow
try {
    $coupons = Invoke-RestMethod -Uri "http://localhost:5009/api/coupon" -Method GET -Headers $headers
    Write-Host "Nombre de coupons: $($coupons.Count)" -ForegroundColor Green
    foreach ($coupon in $coupons) {
        Write-Host "  - $($coupon.Code): $($coupon.Name)" -ForegroundColor White
    }
} catch {
    Write-Host "Erreur API Coupon directe: $($_.Exception.Message)" -ForegroundColor Red
}

# 3. Test via API Gateway
Write-Host "`nTest via API Gateway..." -ForegroundColor Yellow
try {
    $couponsGateway = Invoke-RestMethod -Uri "http://localhost:5000/api/coupons" -Method GET -Headers $headers
    Write-Host "Nombre de coupons via Gateway: $($couponsGateway.Count)" -ForegroundColor Green
} catch {
    Write-Host "Erreur via Gateway: $($_.Exception.Message)" -ForegroundColor Red
}

Write-Host "`n=== Test termine ===" -ForegroundColor Green
