# Script pour tester l'API Coupon
$baseUrl = "http://localhost:5000"

Write-Host "=== Test de l'API Coupon ===" -ForegroundColor Green

# 1. Connexion admin
Write-Host "Connexion en cours..." -ForegroundColor Yellow
$loginData = @{
    Username = "<EMAIL>"
    Password = "Admin123!"
}

try {
    $loginResponse = Invoke-RestMethod -Uri "$baseUrl/api/identity/auth/login" -Method POST -Body ($loginData | ConvertTo-Json) -ContentType "application/json"
    $token = $loginResponse.accessToken
    Write-Host "Connexion reussie, token obtenu" -ForegroundColor Green
} catch {
    Write-Host "Erreur de connexion: $($_.Exception.Message)" -ForegroundColor Red
    Write-Host "Reponse: $($_.Exception.Response)" -ForegroundColor Red
    exit 1
}

# Headers avec token
$headers = @{
    "Authorization" = "Bearer $token"
    "Content-Type" = "application/json"
}

# 2. Lister les coupons existants
Write-Host "`nRecuperation des coupons existants..." -ForegroundColor Yellow
try {
    $coupons = Invoke-RestMethod -Uri "$baseUrl/api/coupons" -Method GET -Headers $headers
    Write-Host "Nombre de coupons existants: $($coupons.Count)" -ForegroundColor Green
    
    if ($coupons.Count -gt 0) {
        Write-Host "Coupons existants:" -ForegroundColor Cyan
        foreach ($coupon in $coupons) {
            Write-Host "  - Code: $($coupon.Code), Nom: $($coupon.Name), Actif: $($coupon.IsActive)" -ForegroundColor White
        }
    }
} catch {
    Write-Host "Erreur lors de la recuperation des coupons: $($_.Exception.Message)" -ForegroundColor Red
}

# 3. Créer un coupon de test BINE10
Write-Host "`nCreation du coupon BINE10..." -ForegroundColor Yellow
$newCoupon = @{
    Code = "BINE10"
    Name = "Reduction 10% BINE"
    Description = "Coupon de test pour 10% de reduction"
    Type = 1  # Percentage
    Value = 10.0
    IsActive = $true
    StartDate = (Get-Date).ToString("yyyy-MM-ddTHH:mm:ss.fffZ")
    EndDate = (Get-Date).AddDays(30).ToString("yyyy-MM-ddTHH:mm:ss.fffZ")
    ApplicableToAllProducts = $true
    Currency = "GNF"
    MinimumOrderAmount = 50000
    MaximumDiscountAmount = 100000
    UsageLimit = 100
    UsageLimitPerUser = 5
}

try {
    $createResponse = Invoke-RestMethod -Uri "$baseUrl/api/coupons" -Method POST -Body ($newCoupon | ConvertTo-Json) -Headers $headers
    Write-Host "Coupon BINE10 cree avec succes! ID: $($createResponse.Id)" -ForegroundColor Green
} catch {
    Write-Host "Erreur lors de la creation du coupon: $($_.Exception.Message)" -ForegroundColor Red
    if ($_.Exception.Response) {
        $errorContent = $_.Exception.Response.Content.ReadAsStringAsync().Result
        Write-Host "Details de l'erreur: $errorContent" -ForegroundColor Red
    }
}

# 4. Tester la validation du coupon
Write-Host "`nTest de validation du coupon BINE10..." -ForegroundColor Yellow
$cartForValidation = @{
    CouponCode = "BINE10"
    Cart = @{
        UserId = "1"
        SubTotal = 100000
        Items = @(
            @{
                ProductId = 1
                Quantity = 1
                UnitPrice = 100000
                CategoryId = 1
                SellerId = 1
            }
        )
    }
}

try {
    $validationResponse = Invoke-RestMethod -Uri "$baseUrl/api/coupons/validate" -Method POST -Body ($cartForValidation | ConvertTo-Json -Depth 3) -Headers $headers
    Write-Host "Validation du coupon:" -ForegroundColor Green
    Write-Host "  - Valide: $($validationResponse.IsValid)" -ForegroundColor White
    Write-Host "  - Montant de reduction: $($validationResponse.DiscountAmount) GNF" -ForegroundColor White
    if (-not $validationResponse.IsValid) {
        Write-Host "  - Erreur: $($validationResponse.ErrorMessage)" -ForegroundColor Red
    }
} catch {
    Write-Host "Erreur lors de la validation du coupon: $($_.Exception.Message)" -ForegroundColor Red
    if ($_.Exception.Response) {
        $errorContent = $_.Exception.Response.Content.ReadAsStringAsync().Result
        Write-Host "Details de l'erreur: $errorContent" -ForegroundColor Red
    }
}

Write-Host "`n=== Test termine ===" -ForegroundColor Green
