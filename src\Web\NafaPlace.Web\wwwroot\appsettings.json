{"ApiEndpoints": {"IdentityApi": "http://localhost:5155", "CatalogApi": "http://localhost:5000/api/catalog", "CartApi": "http://localhost:5000/api/cart", "OrderApi": "http://localhost:5000/api/order", "PaymentApi": "http://localhost:5000/api/payment", "ReviewApi": "http://localhost:5000/api/review", "NotificationsApi": "http://localhost:5000/api/notifications", "WishlistApi": "http://localhost:5000/api/wishlist", "CouponsApi": "http://localhost:5000/api/coupons"}, "Logging": {"LogLevel": {"Default": "Information", "Microsoft.AspNetCore": "Warning", "Microsoft.Hosting.Lifetime": "Information"}}, "AllowedHosts": "*", "Cloudinary": {"CloudName": "${CLOUDINARY_CLOUD_NAME}", "ApiKey": "${CLOUDINARY_API_KEY}", "ApiSecret": "${CLOUDINARY_API_SECRET}"}, "Stripe": {"PublishableKey": "${STRIPE_PUBLISHABLE_KEY}", "SecretKey": "${STRIPE_SECRET_KEY}"}}