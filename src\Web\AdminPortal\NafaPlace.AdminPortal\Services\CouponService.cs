using System.Net.Http.Json;
using System.Text.Json;

namespace NafaPlace.AdminPortal.Services;

public class CouponService
{
    private readonly HttpClient _httpClient;
    private readonly ILogger<CouponService> _logger;
    private readonly string _baseUrl;

    public CouponService(HttpClient httpClient, IConfiguration configuration, ILogger<CouponService> logger)
    {
        _httpClient = httpClient;
        _logger = logger;
        _baseUrl = configuration["ApiSettings:CouponApiUrl"] ?? "http://localhost:5009";
    }

    public async Task<List<CouponDto>> GetCouponsAsync(int page = 1, int pageSize = 20, bool? isActive = null)
    {
        try
        {
            var query = $"?page={page}&pageSize={pageSize}";
            if (isActive.HasValue)
                query += $"&isActive={isActive.Value}";

            var response = await _httpClient.GetAsync($"{_baseUrl}/api/coupons{query}");
            
            if (response.IsSuccessStatusCode)
            {
                var content = await response.Content.ReadAsStringAsync();
                return JsonSerializer.Deserialize<List<CouponDto>>(content, new JsonSerializerOptions
                {
                    PropertyNameCaseInsensitive = true
                }) ?? new List<CouponDto>();
            }
            
            _logger.LogError("Failed to get coupons. Status: {StatusCode}", response.StatusCode);
            return new List<CouponDto>();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting coupons");
            return new List<CouponDto>();
        }
    }

    public async Task<CouponDto?> GetCouponByIdAsync(int id)
    {
        try
        {
            var response = await _httpClient.GetAsync($"{_baseUrl}/api/coupons/{id}");
            
            if (response.IsSuccessStatusCode)
            {
                var content = await response.Content.ReadAsStringAsync();
                return JsonSerializer.Deserialize<CouponDto>(content, new JsonSerializerOptions
                {
                    PropertyNameCaseInsensitive = true
                });
            }
            
            return null;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting coupon {Id}", id);
            return null;
        }
    }

    public async Task<CouponDto?> CreateCouponAsync(CreateCouponRequest request)
    {
        try
        {
            var response = await _httpClient.PostAsJsonAsync($"{_baseUrl}/api/coupons", request);
            
            if (response.IsSuccessStatusCode)
            {
                var content = await response.Content.ReadAsStringAsync();
                return JsonSerializer.Deserialize<CouponDto>(content, new JsonSerializerOptions
                {
                    PropertyNameCaseInsensitive = true
                });
            }
            
            _logger.LogError("Failed to create coupon. Status: {StatusCode}", response.StatusCode);
            return null;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error creating coupon");
            return null;
        }
    }

    public async Task<CouponDto?> UpdateCouponAsync(int id, UpdateCouponRequest request)
    {
        try
        {
            var response = await _httpClient.PutAsJsonAsync($"{_baseUrl}/api/coupons/{id}", request);
            
            if (response.IsSuccessStatusCode)
            {
                var content = await response.Content.ReadAsStringAsync();
                return JsonSerializer.Deserialize<CouponDto>(content, new JsonSerializerOptions
                {
                    PropertyNameCaseInsensitive = true
                });
            }
            
            _logger.LogError("Failed to update coupon. Status: {StatusCode}", response.StatusCode);
            return null;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating coupon {Id}", id);
            return null;
        }
    }

    public async Task<bool> DeleteCouponAsync(int id)
    {
        try
        {
            var response = await _httpClient.DeleteAsync($"{_baseUrl}/api/coupons/{id}");
            return response.IsSuccessStatusCode;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error deleting coupon {Id}", id);
            return false;
        }
    }

    public async Task<CouponValidationResult?> ValidateCouponAsync(string code, decimal orderAmount)
    {
        try
        {
            var request = new
            {
                CouponCode = code,
                Cart = new
                {
                    TotalAmount = orderAmount,
                    Items = new List<object>()
                }
            };

            var response = await _httpClient.PostAsJsonAsync($"{_baseUrl}/api/coupons/validate", request);
            
            if (response.IsSuccessStatusCode)
            {
                var content = await response.Content.ReadAsStringAsync();
                return JsonSerializer.Deserialize<CouponValidationResult>(content, new JsonSerializerOptions
                {
                    PropertyNameCaseInsensitive = true
                });
            }
            
            return null;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error validating coupon {Code}", code);
            return null;
        }
    }

    public async Task<List<CouponUsageStatsDto>> GetUsageStatsAsync(DateTime? startDate = null, DateTime? endDate = null)
    {
        try
        {
            var query = "";
            if (startDate.HasValue || endDate.HasValue)
            {
                query = "?";
                if (startDate.HasValue)
                    query += $"startDate={startDate.Value:yyyy-MM-dd}&";
                if (endDate.HasValue)
                    query += $"endDate={endDate.Value:yyyy-MM-dd}&";
                query = query.TrimEnd('&');
            }

            var response = await _httpClient.GetAsync($"{_baseUrl}/api/coupons/usage-stats{query}");
            
            if (response.IsSuccessStatusCode)
            {
                var content = await response.Content.ReadAsStringAsync();
                return JsonSerializer.Deserialize<List<CouponUsageStatsDto>>(content, new JsonSerializerOptions
                {
                    PropertyNameCaseInsensitive = true
                }) ?? new List<CouponUsageStatsDto>();
            }
            
            return new List<CouponUsageStatsDto>();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting usage stats");
            return new List<CouponUsageStatsDto>();
        }
    }
}

// DTOs
public class CouponDto
{
    public int Id { get; set; }
    public string Code { get; set; } = string.Empty;
    public string Name { get; set; } = string.Empty;
    public string? Description { get; set; }
    public CouponType Type { get; set; }
    public decimal Value { get; set; }
    public decimal? MinimumOrderAmount { get; set; }
    public decimal? MaximumDiscountAmount { get; set; }
    public DateTime StartDate { get; set; }
    public DateTime EndDate { get; set; }
    public int? UsageLimit { get; set; }
    public int? UsageLimitPerUser { get; set; }
    public int UsageCount { get; set; }
    public bool IsActive { get; set; }
    public string Currency { get; set; } = "GNF";
    public bool ApplicableToAllProducts { get; set; }
    public List<int>? ApplicableProductIds { get; set; }
    public List<int>? ApplicableCategoryIds { get; set; }
    public List<int>? ApplicableSellerIds { get; set; }
    public List<int>? ExcludedProductIds { get; set; }
    public List<int>? ExcludedCategoryIds { get; set; }
    public DateTime CreatedAt { get; set; }
    public DateTime UpdatedAt { get; set; }
}

public class CreateCouponRequest
{
    public string Code { get; set; } = string.Empty;
    public string Name { get; set; } = string.Empty;
    public string? Description { get; set; }
    public CouponType Type { get; set; }
    public decimal Value { get; set; }
    public decimal? MinimumOrderAmount { get; set; }
    public decimal? MaximumDiscountAmount { get; set; }
    public DateTime StartDate { get; set; }
    public DateTime EndDate { get; set; }
    public int? UsageLimit { get; set; }
    public int? UsageLimitPerUser { get; set; }
    public bool IsActive { get; set; } = true;
    public string Currency { get; set; } = "GNF";
    public bool ApplicableToAllProducts { get; set; } = true;
    public List<int>? ApplicableProductIds { get; set; }
    public List<int>? ApplicableCategoryIds { get; set; }
    public List<int>? ApplicableSellerIds { get; set; }
    public List<int>? ExcludedProductIds { get; set; }
    public List<int>? ExcludedCategoryIds { get; set; }
}

public class UpdateCouponRequest
{
    public string? Name { get; set; }
    public string? Description { get; set; }
    public decimal? Value { get; set; }
    public decimal? MinimumOrderAmount { get; set; }
    public decimal? MaximumDiscountAmount { get; set; }
    public DateTime? StartDate { get; set; }
    public DateTime? EndDate { get; set; }
    public int? UsageLimit { get; set; }
    public int? UsageLimitPerUser { get; set; }
    public bool? IsActive { get; set; }
    public bool? ApplicableToAllProducts { get; set; }
    public List<int>? ApplicableProductIds { get; set; }
    public List<int>? ApplicableCategoryIds { get; set; }
    public List<int>? ApplicableSellerIds { get; set; }
    public List<int>? ExcludedProductIds { get; set; }
    public List<int>? ExcludedCategoryIds { get; set; }
}

public class CouponValidationResult
{
    public bool IsValid { get; set; }
    public string? ErrorMessage { get; set; }
    public decimal DiscountAmount { get; set; }
    public CouponDto? Coupon { get; set; }
}

public class CouponUsageStatsDto
{
    public int CouponId { get; set; }
    public string CouponCode { get; set; } = string.Empty;
    public string CouponName { get; set; } = string.Empty;
    public int TotalUsages { get; set; }
    public decimal TotalDiscountAmount { get; set; }
    public DateTime LastUsed { get; set; }
}

public enum CouponType
{
    FixedAmount = 1,
    Percentage = 2,
    FreeShipping = 3,
    BuyXGetY = 4
}
