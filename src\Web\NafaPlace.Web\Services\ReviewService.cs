using System.Text;
using System.Text.Json;
using NafaPlace.Reviews.DTOs;
using System.Security.Claims;
using Microsoft.AspNetCore.Components.Authorization;
using Blazored.LocalStorage;
using Microsoft.JSInterop;

namespace NafaPlace.Web.Services;

public class ReviewService : IReviewService
{
    private readonly HttpClient _httpClient;
    private readonly AuthenticationStateProvider _authStateProvider;
    private readonly ILocalStorageService _localStorage;
    private readonly IJSRuntime _jsRuntime;
    private readonly JsonSerializerOptions _jsonOptions;

    public ReviewService(HttpClient httpClient, AuthenticationStateProvider authStateProvider, ILocalStorageService localStorage, IJSRuntime jsRuntime)
    {
        _httpClient = httpClient;
        _authStateProvider = authStateProvider;
        _localStorage = localStorage;
        _jsRuntime = jsRuntime;
        _jsonOptions = new JsonSerializerOptions
        {
            PropertyNamingPolicy = JsonNamingPolicy.CamelCase,
            PropertyNameCaseInsensitive = true
        };
    }

    private async Task SetAuthorizationHeaderAsync()
    {
        try
        {
            var token = await _localStorage.GetItemAsStringAsync("authToken");
            Console.WriteLine($"🎫 DEBUG ReviewService: Token found: {!string.IsNullOrEmpty(token)}, Length: {token?.Length ?? 0}");

            if (!string.IsNullOrEmpty(token))
            {
                // Supprimer les guillemets éventuels autour du token
                token = token.Trim('"');
                Console.WriteLine($"🧹 DEBUG ReviewService: Token after trim: Length: {token.Length}");

                _httpClient.DefaultRequestHeaders.Authorization =
                    new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", token);
                Console.WriteLine("✅ DEBUG ReviewService: Authorization header set successfully");
            }
            else
            {
                Console.WriteLine("⚠️ DEBUG ReviewService: No token found in localStorage");
            }
        }
        catch (Exception ex)
        {
            Console.WriteLine($"❌ DEBUG ReviewService: Erreur lors de la récupération du token: {ex.Message}");
        }
    }

    public async Task<ReviewDto?> GetReviewByIdAsync(int id)
    {
        try
        {
            await SetAuthorizationHeaderAsync();
            var response = await _httpClient.GetAsync($"/api/reviews/{id}");
            
            if (response.IsSuccessStatusCode)
            {
                var content = await response.Content.ReadAsStringAsync();
                return JsonSerializer.Deserialize<ReviewDto>(content, _jsonOptions);
            }
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Erreur lors de la récupération de la review {id}: {ex.Message}");
        }
        
        return null;
    }

    public async Task<ReviewsPagedResult> GetReviewsByProductIdAsync(int productId, int page = 1, int pageSize = 10)
    {
        try
        {
            var response = await _httpClient.GetAsync($"/api/reviews/product/{productId}?page={page}&pageSize={pageSize}");
            
            if (response.IsSuccessStatusCode)
            {
                var content = await response.Content.ReadAsStringAsync();
                return JsonSerializer.Deserialize<ReviewsPagedResult>(content, _jsonOptions) ?? new ReviewsPagedResult();
            }
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Erreur lors de la récupération des reviews du produit {productId}: {ex.Message}");
        }
        
        return new ReviewsPagedResult();
    }

    public async Task<ReviewsPagedResult> GetUserReviewsAsync(string userId, int page = 1, int pageSize = 10)
    {
        try
        {
            await SetAuthorizationHeaderAsync();
            var response = await _httpClient.GetAsync($"/api/reviews/user/{userId}?page={page}&pageSize={pageSize}");
            
            if (response.IsSuccessStatusCode)
            {
                var content = await response.Content.ReadAsStringAsync();
                return JsonSerializer.Deserialize<ReviewsPagedResult>(content, _jsonOptions) ?? new ReviewsPagedResult();
            }
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Erreur lors de la récupération des reviews de l'utilisateur {userId}: {ex.Message}");
        }
        
        return new ReviewsPagedResult();
    }

    public async Task<ReviewDto> CreateReviewAsync(CreateReviewRequest request)
    {
        try
        {
            Console.WriteLine($"🚀 DEBUG ReviewService: Starting CreateReviewAsync for ProductId: {request.ProductId}");

            await SetAuthorizationHeaderAsync();
            var json = JsonSerializer.Serialize(request, _jsonOptions);
            var content = new StringContent(json, Encoding.UTF8, "application/json");

            Console.WriteLine($"📤 DEBUG ReviewService: Sending POST to /api/reviews");
            Console.WriteLine($"📋 DEBUG ReviewService: Request data: {json}");

            var response = await _httpClient.PostAsync("/api/reviews", content);

            Console.WriteLine($"📥 DEBUG ReviewService: Response status: {response.StatusCode}");

            if (response.IsSuccessStatusCode)
            {
                var responseContent = await response.Content.ReadAsStringAsync();
                Console.WriteLine($"✅ DEBUG ReviewService: Success! Response: {responseContent}");
                return JsonSerializer.Deserialize<ReviewDto>(responseContent, _jsonOptions) ?? new ReviewDto();
            }
            else
            {
                var errorContent = await response.Content.ReadAsStringAsync();
                Console.WriteLine($"❌ DEBUG ReviewService: Error {response.StatusCode}: {errorContent}");
                throw new Exception($"Erreur lors de la création de la review: {response.StatusCode} - {errorContent}");
            }
        }
        catch (Exception ex)
        {
            Console.WriteLine($"💥 DEBUG ReviewService: Exception in CreateReviewAsync: {ex.Message}");
            throw;
        }
    }

    public async Task<ReviewDto> UpdateReviewAsync(int id, UpdateReviewRequest request)
    {
        try
        {
            await SetAuthorizationHeaderAsync();
            var json = JsonSerializer.Serialize(request, _jsonOptions);
            var content = new StringContent(json, Encoding.UTF8, "application/json");
            
            var response = await _httpClient.PutAsync($"/api/reviews/{id}", content);
            
            if (response.IsSuccessStatusCode)
            {
                var responseContent = await response.Content.ReadAsStringAsync();
                return JsonSerializer.Deserialize<ReviewDto>(responseContent, _jsonOptions) ?? new ReviewDto();
            }
            else
            {
                var errorContent = await response.Content.ReadAsStringAsync();
                throw new Exception($"Erreur lors de la mise à jour de la review: {errorContent}");
            }
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Erreur lors de la mise à jour de la review {id}: {ex.Message}");
            throw;
        }
    }

    public async Task DeleteReviewAsync(int id)
    {
        try
        {
            await SetAuthorizationHeaderAsync();
            var response = await _httpClient.DeleteAsync($"/api/reviews/{id}");
            
            if (!response.IsSuccessStatusCode)
            {
                var errorContent = await response.Content.ReadAsStringAsync();
                throw new Exception($"Erreur lors de la suppression de la review: {errorContent}");
            }
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Erreur lors de la suppression de la review {id}: {ex.Message}");
            throw;
        }
    }

    public async Task<ReviewSummaryDto> GetReviewSummaryAsync(int productId)
    {
        try
        {
            var response = await _httpClient.GetAsync($"/api/reviews/product/{productId}/summary");
            
            if (response.IsSuccessStatusCode)
            {
                var content = await response.Content.ReadAsStringAsync();
                return JsonSerializer.Deserialize<ReviewSummaryDto>(content, _jsonOptions) ?? new ReviewSummaryDto();
            }
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Erreur lors de la récupération du résumé des reviews du produit {productId}: {ex.Message}");
        }
        
        return new ReviewSummaryDto();
    }

    public async Task<bool> MarkReviewHelpfulAsync(int reviewId)
    {
        try
        {
            await SetAuthorizationHeaderAsync();
            var response = await _httpClient.PostAsync($"/api/reviews/{reviewId}/helpful", null);
            return response.IsSuccessStatusCode;
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Erreur lors du marquage de la review {reviewId} comme utile: {ex.Message}");
            return false;
        }
    }

    public async Task<bool> UnmarkReviewHelpfulAsync(int reviewId)
    {
        try
        {
            await SetAuthorizationHeaderAsync();
            var response = await _httpClient.DeleteAsync($"/api/reviews/{reviewId}/helpful");
            return response.IsSuccessStatusCode;
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Erreur lors du démarquage de la review {reviewId} comme utile: {ex.Message}");
            return false;
        }
    }

    public async Task<bool> CanUserReviewProductAsync(int productId, string userId)
    {
        try
        {
            await SetAuthorizationHeaderAsync();
            var response = await _httpClient.GetAsync($"/api/reviews/product/{productId}/can-review");

            if (response.IsSuccessStatusCode)
            {
                var content = await response.Content.ReadAsStringAsync();
                return JsonSerializer.Deserialize<bool>(content, _jsonOptions);
            }
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Erreur lors de la vérification si l'utilisateur peut laisser un avis: {ex.Message}");
        }

        return false;
    }

    public async Task<List<ReplyDto>> GetReviewRepliesAsync(int reviewId)
    {
        try
        {
            var response = await _httpClient.GetAsync($"/api/reviews/{reviewId}/replies");
            
            if (response.IsSuccessStatusCode)
            {
                var content = await response.Content.ReadAsStringAsync();
                return JsonSerializer.Deserialize<List<ReplyDto>>(content, _jsonOptions) ?? new List<ReplyDto>();
            }
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Erreur lors de la récupération des réponses de la review {reviewId}: {ex.Message}");
        }
        
        return new List<ReplyDto>();
    }

    public async Task<ReplyDto> CreateReplyAsync(CreateReplyRequest request)
    {
        try
        {
            await SetAuthorizationHeaderAsync();
            var json = JsonSerializer.Serialize(request, _jsonOptions);
            var content = new StringContent(json, Encoding.UTF8, "application/json");
            
            var response = await _httpClient.PostAsync("/api/reviews/replies", content);
            
            if (response.IsSuccessStatusCode)
            {
                var responseContent = await response.Content.ReadAsStringAsync();
                return JsonSerializer.Deserialize<ReplyDto>(responseContent, _jsonOptions) ?? new ReplyDto();
            }
            else
            {
                var errorContent = await response.Content.ReadAsStringAsync();
                throw new Exception($"Erreur lors de la création de la réponse: {errorContent}");
            }
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Erreur lors de la création de la réponse: {ex.Message}");
            throw;
        }
    }

    public async Task DeleteReplyAsync(int replyId)
    {
        try
        {
            await SetAuthorizationHeaderAsync();
            var response = await _httpClient.DeleteAsync($"/api/reviews/replies/{replyId}");
            
            if (!response.IsSuccessStatusCode)
            {
                var errorContent = await response.Content.ReadAsStringAsync();
                throw new Exception($"Erreur lors de la suppression de la réponse: {errorContent}");
            }
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Erreur lors de la suppression de la réponse {replyId}: {ex.Message}");
            throw;
        }
    }
}
