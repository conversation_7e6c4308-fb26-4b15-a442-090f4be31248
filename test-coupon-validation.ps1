# Test de validation du coupon BINE10
Write-Host "=== Test de validation du coupon BINE10 ===" -ForegroundColor Green

# 1. Login pour obtenir un token
$loginData = @{
    Username = "<EMAIL>"
    Password = "Admin123!"
}

try {
    $loginResponse = Invoke-RestMethod -Uri "http://localhost:5155/api/auth/login" -Method POST -Body ($loginData | ConvertTo-Json) -ContentType "application/json"
    $token = $loginResponse.accessToken
    Write-Host "Token obtenu: $($token.Substring(0, 50))..." -ForegroundColor Green
} catch {
    Write-Host "Erreur de login: $($_.Exception.Message)" -ForegroundColor Red
    exit 1
}

$headers = @{
    "Authorization" = "Bearer $token"
    "Content-Type" = "application/json"
}

# 2. Test de validation du coupon BINE10
Write-Host "`nTest de validation du coupon BINE10..." -ForegroundColor Yellow
$cartForValidation = @{
    CouponCode = "BINE10"
    Cart = @{
        UserId = "1"
        SubTotal = 100000
        Items = @(
            @{
                ProductId = 1
                Quantity = 1
                UnitPrice = 100000
                CategoryId = 1
                SellerId = 1
            }
        )
    }
}

try {
    $validationResponse = Invoke-RestMethod -Uri "http://localhost:5000/api/coupons/validate" -Method POST -Body ($cartForValidation | ConvertTo-Json -Depth 3) -Headers $headers
    Write-Host "Validation du coupon:" -ForegroundColor Green
    Write-Host "  - Valide: $($validationResponse.IsValid)" -ForegroundColor White
    Write-Host "  - Montant de reduction: $($validationResponse.DiscountAmount) GNF" -ForegroundColor White
    if (-not $validationResponse.IsValid) {
        Write-Host "  - Erreur: $($validationResponse.ErrorMessage)" -ForegroundColor Red
    }
} catch {
    Write-Host "Erreur lors de la validation du coupon: $($_.Exception.Message)" -ForegroundColor Red
    if ($_.Exception.Response) {
        $reader = New-Object System.IO.StreamReader($_.Exception.Response.GetResponseStream())
        $errorContent = $reader.ReadToEnd()
        Write-Host "Details de l'erreur: $errorContent" -ForegroundColor Red
    }
}

# 3. Test d'application du coupon BINE10
Write-Host "`nTest d'application du coupon BINE10..." -ForegroundColor Yellow
try {
    $applicationResponse = Invoke-RestMethod -Uri "http://localhost:5000/api/coupons/apply" -Method POST -Body ($cartForValidation | ConvertTo-Json -Depth 3) -Headers $headers
    Write-Host "Application du coupon:" -ForegroundColor Green
    Write-Host "  - Succes: $($applicationResponse.Success)" -ForegroundColor White
    Write-Host "  - Montant de reduction: $($applicationResponse.DiscountAmount) GNF" -ForegroundColor White
    Write-Host "  - Nouveau sous-total: $($applicationResponse.NewSubTotal) GNF" -ForegroundColor White
    Write-Host "  - Nouveau total: $($applicationResponse.NewTotal) GNF" -ForegroundColor White
    if (-not $applicationResponse.Success) {
        Write-Host "  - Erreur: $($applicationResponse.ErrorMessage)" -ForegroundColor Red
    }
} catch {
    Write-Host "Erreur lors de l'application du coupon: $($_.Exception.Message)" -ForegroundColor Red
    if ($_.Exception.Response) {
        $reader = New-Object System.IO.StreamReader($_.Exception.Response.GetResponseStream())
        $errorContent = $reader.ReadToEnd()
        Write-Host "Details de l'erreur: $errorContent" -ForegroundColor Red
    }
}

Write-Host "`n=== Test termine ===" -ForegroundColor Green
