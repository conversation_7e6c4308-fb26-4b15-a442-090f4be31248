{"Logging": {"LogLevel": {"Default": "Information", "Microsoft.AspNetCore": "Warning"}}, "AllowedHosts": "*", "ConnectionStrings": {"DefaultConnection": "Host=localhost;Port=5439;Database=NafaPlace.Coupon;Username=postgres;Password=*****************"}, "IdentityUrl": "http://localhost:5155", "JwtSettings": {"Secret": "*****************SecretKeyForJWTTokenGeneration", "Issuer": "NafaPlace", "Audience": "NafaPlaceApi", "ExpiryInMinutes": 60}}