﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace NafaPlace.Coupon.Infrastructure.Migrations
{
    /// <inheritdoc />
    public partial class CreateCouponTables : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.UpdateData(
                table: "Coupons",
                keyColumn: "Id",
                keyValue: 1,
                columns: new[] { "CreatedAt", "EndDate", "StartDate", "UpdatedAt" },
                values: new object[] { new DateTime(2025, 7, 18, 0, 25, 11, 541, DateTimeKind.Utc).AddTicks(7587), new DateTime(2026, 7, 18, 0, 25, 11, 541, DateTimeKind.Utc).AddTicks(7139), new DateTime(2025, 6, 18, 0, 25, 11, 541, DateTimeKind.Utc).AddTicks(7030), new DateTime(2025, 7, 18, 0, 25, 11, 541, DateTimeKind.Utc).AddTicks(7650) });

            migrationBuilder.UpdateData(
                table: "Coupons",
                keyColumn: "Id",
                keyValue: 2,
                columns: new[] { "CreatedAt", "EndDate", "StartDate", "UpdatedAt" },
                values: new object[] { new DateTime(2025, 7, 18, 0, 25, 11, 541, DateTimeKind.Utc).AddTicks(7712), new DateTime(2025, 8, 17, 0, 25, 11, 541, DateTimeKind.Utc).AddTicks(7711), new DateTime(2025, 7, 11, 0, 25, 11, 541, DateTimeKind.Utc).AddTicks(7710), new DateTime(2025, 7, 18, 0, 25, 11, 541, DateTimeKind.Utc).AddTicks(7713) });

            migrationBuilder.UpdateData(
                table: "Coupons",
                keyColumn: "Id",
                keyValue: 3,
                columns: new[] { "CreatedAt", "EndDate", "StartDate", "UpdatedAt" },
                values: new object[] { new DateTime(2025, 7, 18, 0, 25, 11, 541, DateTimeKind.Utc).AddTicks(7717), new DateTime(2025, 8, 1, 0, 25, 11, 541, DateTimeKind.Utc).AddTicks(7716), new DateTime(2025, 7, 18, 0, 25, 11, 541, DateTimeKind.Utc).AddTicks(7715), new DateTime(2025, 7, 18, 0, 25, 11, 541, DateTimeKind.Utc).AddTicks(7717) });
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.UpdateData(
                table: "Coupons",
                keyColumn: "Id",
                keyValue: 1,
                columns: new[] { "CreatedAt", "EndDate", "StartDate", "UpdatedAt" },
                values: new object[] { new DateTime(2025, 7, 18, 0, 22, 39, 211, DateTimeKind.Utc).AddTicks(410), new DateTime(2026, 7, 18, 0, 22, 39, 210, DateTimeKind.Utc).AddTicks(9977), new DateTime(2025, 6, 18, 0, 22, 39, 210, DateTimeKind.Utc).AddTicks(9872), new DateTime(2025, 7, 18, 0, 22, 39, 211, DateTimeKind.Utc).AddTicks(469) });

            migrationBuilder.UpdateData(
                table: "Coupons",
                keyColumn: "Id",
                keyValue: 2,
                columns: new[] { "CreatedAt", "EndDate", "StartDate", "UpdatedAt" },
                values: new object[] { new DateTime(2025, 7, 18, 0, 22, 39, 211, DateTimeKind.Utc).AddTicks(534), new DateTime(2025, 8, 17, 0, 22, 39, 211, DateTimeKind.Utc).AddTicks(533), new DateTime(2025, 7, 11, 0, 22, 39, 211, DateTimeKind.Utc).AddTicks(532), new DateTime(2025, 7, 18, 0, 22, 39, 211, DateTimeKind.Utc).AddTicks(535) });

            migrationBuilder.UpdateData(
                table: "Coupons",
                keyColumn: "Id",
                keyValue: 3,
                columns: new[] { "CreatedAt", "EndDate", "StartDate", "UpdatedAt" },
                values: new object[] { new DateTime(2025, 7, 18, 0, 22, 39, 211, DateTimeKind.Utc).AddTicks(539), new DateTime(2025, 8, 1, 0, 22, 39, 211, DateTimeKind.Utc).AddTicks(537), new DateTime(2025, 7, 18, 0, 22, 39, 211, DateTimeKind.Utc).AddTicks(537), new DateTime(2025, 7, 18, 0, 22, 39, 211, DateTimeKind.Utc).AddTicks(539) });
        }
    }
}
