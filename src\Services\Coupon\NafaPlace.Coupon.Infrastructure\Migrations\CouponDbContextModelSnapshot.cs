﻿// <auto-generated />
using System;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Infrastructure;
using Microsoft.EntityFrameworkCore.Storage.ValueConversion;
using NafaPlace.Coupon.Infrastructure.Data;
using Npgsql.EntityFrameworkCore.PostgreSQL.Metadata;

#nullable disable

namespace NafaPlace.Coupon.Infrastructure.Migrations
{
    [DbContext(typeof(CouponDbContext))]
    partial class CouponDbContextModelSnapshot : ModelSnapshot
    {
        protected override void BuildModel(ModelBuilder modelBuilder)
        {
#pragma warning disable 612, 618
            modelBuilder
                .HasAnnotation("ProductVersion", "9.0.0")
                .HasAnnotation("Relational:MaxIdentifierLength", 63);

            NpgsqlModelBuilderExtensions.UseIdentityByDefaultColumns(modelBuilder);

            modelBuilder.Entity("NafaPlace.Coupon.Domain.Models.Coupon", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<string>("ApplicableCategoryIds")
                        .HasColumnType("text");

                    b.Property<string>("ApplicableProductIds")
                        .HasColumnType("text");

                    b.Property<string>("ApplicableSellerIds")
                        .HasColumnType("text");

                    b.Property<bool>("ApplicableToAllProducts")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("boolean")
                        .HasDefaultValue(true);

                    b.Property<string>("Code")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("CreatedBy")
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.Property<string>("Currency")
                        .IsRequired()
                        .ValueGeneratedOnAdd()
                        .HasMaxLength(3)
                        .HasColumnType("character varying(3)")
                        .HasDefaultValue("GNF");

                    b.Property<string>("Description")
                        .HasMaxLength(500)
                        .HasColumnType("character varying(500)");

                    b.Property<DateTime>("EndDate")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("ExcludedCategoryIds")
                        .HasColumnType("text");

                    b.Property<string>("ExcludedProductIds")
                        .HasColumnType("text");

                    b.Property<string>("ExcludedSellerIds")
                        .HasColumnType("text");

                    b.Property<bool>("IsActive")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("boolean")
                        .HasDefaultValue(true);

                    b.Property<decimal?>("MaximumDiscountAmount")
                        .HasColumnType("decimal(18,2)");

                    b.Property<decimal?>("MinOrderAmount")
                        .HasColumnType("numeric");

                    b.Property<decimal?>("MinimumOrderAmount")
                        .HasColumnType("decimal(18,2)");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(200)
                        .HasColumnType("character varying(200)");

                    b.Property<DateTime>("StartDate")
                        .HasColumnType("timestamp with time zone");

                    b.Property<int>("Type")
                        .HasColumnType("integer");

                    b.Property<DateTime>("UpdatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("UpdatedBy")
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.Property<int>("UsageCount")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer")
                        .HasDefaultValue(0);

                    b.Property<int?>("UsageLimit")
                        .HasColumnType("integer");

                    b.Property<int?>("UsageLimitPerUser")
                        .HasColumnType("integer");

                    b.Property<decimal>("Value")
                        .HasColumnType("decimal(18,2)");

                    b.HasKey("Id");

                    b.HasIndex("Code")
                        .IsUnique();

                    b.HasIndex("EndDate");

                    b.HasIndex("IsActive");

                    b.HasIndex("StartDate");

                    b.HasIndex("IsActive", "StartDate", "EndDate");

                    b.ToTable("Coupons");

                    b.HasData(
                        new
                        {
                            Id = 1,
                            ApplicableToAllProducts = true,
                            Code = "WELCOME10",
                            CreatedAt = new DateTime(2025, 7, 18, 0, 25, 11, 541, DateTimeKind.Utc).AddTicks(7587),
                            CreatedBy = "System",
                            Currency = "GNF",
                            Description = "10% de réduction pour les nouveaux clients",
                            EndDate = new DateTime(2026, 7, 18, 0, 25, 11, 541, DateTimeKind.Utc).AddTicks(7139),
                            IsActive = true,
                            MaximumDiscountAmount = 100000m,
                            MinimumOrderAmount = 50000m,
                            Name = "Bienvenue - 10% de réduction",
                            StartDate = new DateTime(2025, 6, 18, 0, 25, 11, 541, DateTimeKind.Utc).AddTicks(7030),
                            Type = 2,
                            UpdatedAt = new DateTime(2025, 7, 18, 0, 25, 11, 541, DateTimeKind.Utc).AddTicks(7650),
                            UsageCount = 0,
                            UsageLimit = 1000,
                            UsageLimitPerUser = 1,
                            Value = 10m
                        },
                        new
                        {
                            Id = 2,
                            ApplicableToAllProducts = true,
                            Code = "FREESHIP",
                            CreatedAt = new DateTime(2025, 7, 18, 0, 25, 11, 541, DateTimeKind.Utc).AddTicks(7712),
                            CreatedBy = "System",
                            Currency = "GNF",
                            Description = "Livraison gratuite pour toute commande",
                            EndDate = new DateTime(2025, 8, 17, 0, 25, 11, 541, DateTimeKind.Utc).AddTicks(7711),
                            IsActive = true,
                            MinimumOrderAmount = 100000m,
                            Name = "Livraison gratuite",
                            StartDate = new DateTime(2025, 7, 11, 0, 25, 11, 541, DateTimeKind.Utc).AddTicks(7710),
                            Type = 3,
                            UpdatedAt = new DateTime(2025, 7, 18, 0, 25, 11, 541, DateTimeKind.Utc).AddTicks(7713),
                            UsageCount = 0,
                            UsageLimit = 500,
                            Value = 25000m
                        },
                        new
                        {
                            Id = 3,
                            ApplicableToAllProducts = true,
                            Code = "SAVE50K",
                            CreatedAt = new DateTime(2025, 7, 18, 0, 25, 11, 541, DateTimeKind.Utc).AddTicks(7717),
                            CreatedBy = "System",
                            Currency = "GNF",
                            Description = "50,000 GNF de réduction sur votre commande",
                            EndDate = new DateTime(2025, 8, 1, 0, 25, 11, 541, DateTimeKind.Utc).AddTicks(7716),
                            IsActive = true,
                            MinimumOrderAmount = 200000m,
                            Name = "Économisez 50,000 GNF",
                            StartDate = new DateTime(2025, 7, 18, 0, 25, 11, 541, DateTimeKind.Utc).AddTicks(7715),
                            Type = 1,
                            UpdatedAt = new DateTime(2025, 7, 18, 0, 25, 11, 541, DateTimeKind.Utc).AddTicks(7717),
                            UsageCount = 0,
                            UsageLimit = 100,
                            UsageLimitPerUser = 1,
                            Value = 50000m
                        });
                });

            modelBuilder.Entity("NafaPlace.Coupon.Domain.Models.CouponUsage", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<int>("CouponId")
                        .HasColumnType("integer");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("CreatedBy")
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.Property<string>("Currency")
                        .IsRequired()
                        .ValueGeneratedOnAdd()
                        .HasMaxLength(3)
                        .HasColumnType("character varying(3)")
                        .HasDefaultValue("GNF");

                    b.Property<decimal>("DiscountAmount")
                        .HasColumnType("decimal(18,2)");

                    b.Property<string>("OrderId")
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.Property<DateTime>("UpdatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("UpdatedBy")
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.Property<DateTime>("UsedAt")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("timestamp with time zone")
                        .HasDefaultValueSql("CURRENT_TIMESTAMP");

                    b.Property<string>("UserId")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.HasKey("Id");

                    b.HasIndex("CouponId");

                    b.HasIndex("OrderId");

                    b.HasIndex("UsedAt");

                    b.HasIndex("UserId");

                    b.HasIndex("CouponId", "UserId");

                    b.ToTable("CouponUsages");
                });

            modelBuilder.Entity("NafaPlace.Coupon.Domain.Models.CouponUsage", b =>
                {
                    b.HasOne("NafaPlace.Coupon.Domain.Models.Coupon", "Coupon")
                        .WithMany("Usages")
                        .HasForeignKey("CouponId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Coupon");
                });

            modelBuilder.Entity("NafaPlace.Coupon.Domain.Models.Coupon", b =>
                {
                    b.Navigation("Usages");
                });
#pragma warning restore 612, 618
        }
    }
}
