using System.Net.Http.Json;
using System.Text.Json;
using NafaPlace.Reviews.Application.DTOs;
using NafaPlace.Reviews.Application.Interfaces;

namespace NafaPlace.Reviews.Infrastructure.Services;

public class ProductInfoService : IProductInfoService
{
    private readonly HttpClient _httpClient;
    private readonly JsonSerializerOptions _jsonOptions;

    public ProductInfoService(IHttpClientFactory httpClientFactory)
    {
        _httpClient = httpClientFactory.CreateClient("CatalogApi");
        _jsonOptions = new JsonSerializerOptions
        {
            PropertyNamingPolicy = JsonNamingPolicy.CamelCase
        };
    }

    public async Task<ProductInfoDto?> GetProductInfoAsync(int productId)
    {
        try
        {
            Console.WriteLine($"🔍 Récupération des informations du produit {productId} depuis l'API Catalog");
            
            var response = await _httpClient.GetAsync($"api/products/{productId}");
            
            if (!response.IsSuccessStatusCode)
            {
                Console.WriteLine($"❌ Erreur lors de la récupération du produit {productId}: {response.StatusCode}");
                return null;
            }

            var content = await response.Content.ReadAsStringAsync();
            Console.WriteLine($"📦 Réponse de l'API Catalog pour le produit {productId}: {content}");

            var productInfo = JsonSerializer.Deserialize<ProductInfoDto>(content, _jsonOptions);
            
            if (productInfo != null)
            {
                Console.WriteLine($"✅ Informations du produit {productId} récupérées: {productInfo.Name}");
            }
            
            return productInfo;
        }
        catch (Exception ex)
        {
            Console.WriteLine($"❌ Exception lors de la récupération du produit {productId}: {ex.Message}");
            return null;
        }
    }
}
